#!/usr/bin/env python3
"""
Enhanced Business Spreadsheet for IRD Compliance
Preserves existing Google Forms integration while adding IRD-specific features
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
import warnings
warnings.filterwarnings('ignore')

def create_enhanced_spreadsheet():
    """Create enhanced spreadsheet with IRD compliance features"""
    
    # Read existing spreadsheet
    print("Reading existing spreadsheet...")
    excel_file = pd.ExcelFile('Calmren-master.xlsx')
    
    # Create new workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # 1. Copy existing sheets with enhancements
    copy_and_enhance_existing_sheets(excel_file, wb)
    
    # 2. Create new IRD-specific sheets
    create_ird_summary_sheet(wb)
    create_monthly_snapshots_sheet(wb)
    create_bank_import_sheet(wb)
    create_home_office_sheet(wb)
    
    # 3. Save enhanced spreadsheet
    output_file = 'Calmren-Enhanced-IRD.xlsx'
    wb.save(output_file)
    print(f"Enhanced spreadsheet saved as: {output_file}")
    
    return output_file

def copy_and_enhance_existing_sheets(excel_file, wb):
    """Copy existing sheets and enhance them for IRD compliance"""
    
    # Enhanced Expenses Sheet
    print("Enhancing Expenses sheet...")
    expenses_df = pd.read_excel('Calmren-master.xlsx', sheet_name='Expenses')
    
    # Add new IRD compliance columns
    if 'Business Use %' not in expenses_df.columns:
        expenses_df['Business Use %'] = 100  # Default to 100% business use
    if 'IRD Category' not in expenses_df.columns:
        expenses_df['IRD Category'] = ''
    if 'Deductible Amount' not in expenses_df.columns:
        expenses_df['Deductible Amount'] = 0
    if 'Home Office Portion' not in expenses_df.columns:
        expenses_df['Home Office Portion'] = 'No'
    
    # Create enhanced expenses sheet
    ws_expenses = wb.create_sheet("Expenses Enhanced")
    
    # Add headers with IRD categories
    headers = [
        'Timestamp', 'Date', 'Category', 'Supplier', 'Description', 
        'Amount (excl. GST)', 'GST (15%) Included?', 'Total Amount', 
        'Business Use %', 'IRD Category', 'Deductible Amount', 
        'Home Office Portion', 'Receipt', 'Purchase Type'
    ]
    
    for col, header in enumerate(headers, 1):
        cell = ws_expenses.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    
    # Add IRD expense categories as data validation
    add_ird_expense_categories(ws_expenses)
    
    # Copy existing Time Log
    print("Copying Time Log...")
    time_log_df = pd.read_excel('Calmren-master.xlsx', sheet_name='Time Log')
    ws_time = wb.create_sheet("Time Log")
    copy_dataframe_to_sheet(time_log_df, ws_time, "Time Log Data")
    
    # Enhanced GST Sheet
    print("Enhancing GST sheet...")
    create_enhanced_gst_sheet(wb)
    
    # Copy other existing sheets
    for sheet_name in ['Invoicing Dashboard', 'Tax Year Summary', 'Monthly Totals', 
                       'Invoice Log', 'Income Log', 'Vehicle Log', 'Assets']:
        if sheet_name in excel_file.sheet_names:
            print(f"Copying {sheet_name}...")
            df = pd.read_excel('Calmren-master.xlsx', sheet_name=sheet_name)
            ws = wb.create_sheet(sheet_name)
            copy_dataframe_to_sheet(df, ws, f"{sheet_name} Data")

def add_ird_expense_categories(ws):
    """Add IRD expense categories for proper tax classification"""
    
    # IRD expense categories for sole traders
    categories = [
        "Accounting & Legal Fees",
        "Advertising & Marketing", 
        "Bank Charges",
        "Computer & Software",
        "Depreciation",
        "Home Office Expenses",
        "Insurance",
        "Interest on Business Loans",
        "Motor Vehicle Expenses",
        "Office Supplies",
        "Professional Development",
        "Rent & Utilities",
        "Repairs & Maintenance",
        "Subscriptions & Memberships",
        "Telecommunications",
        "Travel & Accommodation",
        "Other Business Expenses"
    ]
    
    # Add categories starting from row 20 for reference
    cell = ws.cell(row=20, column=1)
    cell.value = "IRD Expense Categories:"
    cell.font = Font(bold=True)
    for i, category in enumerate(categories, 21):
        ws.cell(row=i, column=1).value = category

def create_enhanced_gst_sheet(wb):
    """Create enhanced GST sheet with quarterly reporting"""
    
    ws = wb.create_sheet("GST Enhanced")
    
    # GST Summary section
    ws['A1'] = "GST Summary & Quarterly Returns"
    ws['A1'].font = Font(size=16, bold=True)
    
    ws['A3'] = "GST Registration Status"
    ws['B3'] = "Yes"
    ws['A4'] = "GST Rate"
    ws['B4'] = 0.15
    ws['A5'] = "GST Number"
    ws['B5'] = "Enter your GST number"
    
    # Quarterly GST calculation
    quarters = [
        ("Q1 (Apr-Jun)", "A8"),
        ("Q2 (Jul-Sep)", "A15"), 
        ("Q3 (Oct-Dec)", "A22"),
        ("Q4 (Jan-Mar)", "A29")
    ]
    
    for quarter, start_cell in quarters:
        row = int(start_cell[1:])
        ws[start_cell] = quarter
        ws[start_cell].font = Font(bold=True)
        
        ws[f'A{row+1}'] = "GST Collected on Sales"
        ws[f'A{row+2}'] = "GST Paid on Purchases" 
        ws[f'A{row+3}'] = "Net GST Payable/(Refund)"
        ws[f'A{row+4}'] = "Due Date"
        
        # Add formulas (will be populated when data is available)
        ws[f'B{row+3}'] = f"=B{row+1}-B{row+2}"

def copy_dataframe_to_sheet(df, ws, table_name):
    """Copy dataframe to worksheet with formatting"""
    
    # Add data
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # Format headers
    for cell in ws[1]:
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

def create_ird_summary_sheet(wb):
    """Create comprehensive IRD summary dashboard"""

    ws = wb.create_sheet("IRD Summary Dashboard")

    # Title
    ws['A1'] = "IRD Tax Return Summary"
    ws['A1'].font = Font(size=18, bold=True)
    ws.merge_cells('A1:F1')

    # Tax year info
    ws['A3'] = "Tax Year:"
    ws['B3'] = "2025-2026"
    ws['A4'] = "Period:"
    ws['B4'] = "1 April 2025 - 31 March 2026"

    # Income Summary
    ws['A7'] = "INCOME SUMMARY"
    ws['A7'].font = Font(size=14, bold=True)

    income_items = [
        ("Total Invoiced Income", "=SUM('Invoice Log'!D:D)"),
        ("Less: GST on Sales", "=SUM('Invoice Log'!E:E)"),
        ("Net Business Income", "=B8-B9"),
        ("Other Income", "0"),
        ("Total Taxable Income", "=B10+B11")
    ]

    for i, (label, formula) in enumerate(income_items, 8):
        ws[f'A{i}'] = label
        ws[f'B{i}'] = formula if formula.startswith('=') else float(formula)

    # Expense Summary
    ws['A15'] = "EXPENSE SUMMARY"
    ws['A15'].font = Font(size=14, bold=True)

    expense_categories = [
        "Accounting & Legal Fees",
        "Advertising & Marketing",
        "Home Office Expenses (30%)",
        "Motor Vehicle Expenses",
        "Office Supplies",
        "Professional Development",
        "Telecommunications",
        "Other Business Expenses"
    ]

    for i, category in enumerate(expense_categories, 16):
        ws[f'A{i}'] = category
        ws[f'B{i}'] = f"=SUMIF('Expenses Enhanced'!J:J,\"{category}\",'Expenses Enhanced'!K:K)"

    ws[f'A{16+len(expense_categories)}'] = "Total Deductible Expenses"
    ws[f'B{16+len(expense_categories)}'] = f"=SUM(B16:B{15+len(expense_categories)})"

    # Net Profit
    row = 16 + len(expense_categories) + 2
    ws[f'A{row}'] = "NET PROFIT BEFORE TAX"
    ws[f'A{row}'].font = Font(size=14, bold=True)
    ws[f'B{row}'] = f"=B12-B{16+len(expense_categories)}"

    # Tax calculations
    ws[f'A{row+2}'] = "Income Tax (28%)"
    ws[f'B{row+2}'] = f"=B{row}*0.28"
    ws[f'A{row+3}'] = "ACC Levy (1.4%)"
    ws[f'B{row+3}'] = f"=B{row}*0.014"
    ws[f'A{row+4}'] = "Total Tax Liability"
    ws[f'B{row+4}'] = f"=B{row+2}+B{row+3}"

def create_monthly_snapshots_sheet(wb):
    """Create monthly snapshot reporting for IRD"""

    ws = wb.create_sheet("Monthly Snapshots")

    # Title
    ws['A1'] = "Monthly Business Snapshots"
    ws['A1'].font = Font(size=16, bold=True)

    # Headers
    headers = ['Month', 'Income (ex GST)', 'GST Collected', 'Expenses (ex GST)',
               'GST Paid', 'Net GST', 'Profit/Loss', 'Cumulative Profit']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

    # Add months for current tax year
    months = [
        'Apr 2025', 'May 2025', 'Jun 2025', 'Jul 2025', 'Aug 2025', 'Sep 2025',
        'Oct 2025', 'Nov 2025', 'Dec 2025', 'Jan 2026', 'Feb 2026', 'Mar 2026'
    ]

    for i, month in enumerate(months, 4):
        ws[f'A{i}'] = month
        # Add formulas to calculate monthly totals
        ws[f'B{i}'] = f'=SUMIFS(\'Income Log\'!F:F,\'Income Log\'!A:A,">="&DATE(2025,{4+(i-4)},1),\'Income Log\'!A:A,"<"&DATE(2025,{5+(i-4)},1))'
        ws[f'G{i}'] = f'=B{i}-D{i}'  # Profit/Loss
        ws[f'H{i}'] = f'=G{i}+H{i-1}' if i > 4 else f'=G{i}'  # Cumulative

def create_bank_import_sheet(wb):
    """Create framework for bank CSV import"""

    ws = wb.create_sheet("Bank Import Helper")

    # Title
    ws['A1'] = "Bank Statement Import Helper"
    ws['A1'].font = Font(size=16, bold=True)

    # Instructions
    ws['A3'] = "Instructions:"
    ws['A4'] = "1. Export bank statements as CSV"
    ws['A5'] = "2. Paste data starting from row 10"
    ws['A6'] = "3. Use formulas to match transactions to expenses"
    ws['A7'] = "4. Review and categorize unmatched items"

    # Headers for bank data
    bank_headers = ['Date', 'Description', 'Amount', 'Balance', 'Category',
                   'Matched to Expense?', 'Business/Personal', 'Notes']

    for col, header in enumerate(bank_headers, 1):
        cell = ws.cell(row=10, column=col)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

def create_home_office_sheet(wb):
    """Create home office deduction calculator"""

    ws = wb.create_sheet("Home Office Calculator")

    # Title
    ws['A1'] = "Home Office Deduction Calculator (30% Method)"
    ws['A1'].font = Font(size=16, bold=True)

    # Home office details
    ws['A3'] = "Home Office Setup"
    ws['A4'] = "Office area (sqm):"
    ws['B4'] = 20  # Default
    ws['A5'] = "Total home area (sqm):"
    ws['B5'] = 150  # Default
    ws['A6'] = "Business use percentage:"
    ws['B6'] = "=B4/B5"
    ws['A7'] = "IRD Standard Rate (30%):"
    ws['B7'] = 0.30

    # Deductible expenses
    ws['A10'] = "Home Expenses (Annual)"
    home_expenses = [
        "Rent/Mortgage Interest",
        "Rates",
        "Insurance",
        "Power",
        "Phone/Internet",
        "Repairs & Maintenance"
    ]

    for i, expense in enumerate(home_expenses, 11):
        ws[f'A{i}'] = expense
        ws[f'B{i}'] = 0  # User to input
        ws[f'C{i}'] = f"=B{i}*$B$7"  # 30% deduction

    ws[f'A{11+len(home_expenses)}'] = "Total Home Office Deduction"
    ws[f'C{11+len(home_expenses)}'] = f"=SUM(C11:C{10+len(home_expenses)})"

    # Format currency columns
    for row in range(11, 11+len(home_expenses)+1):
        ws[f'B{row}'].number_format = '$#,##0.00'
        ws[f'C{row}'].number_format = '$#,##0.00'

if __name__ == "__main__":
    create_enhanced_spreadsheet()
